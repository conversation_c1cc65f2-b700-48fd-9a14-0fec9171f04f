<template>
  <div class="admin-test-page">
    <div class="admin-test-header">
      <h1 class="admin-test-title">Product Components Test Page</h1>
      <p class="admin-test-description">
        This page tests all the new product management components
      </p>
    </div>

    <div class="admin-test-sections">
      <!-- Test AdminProductHeader -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminProductHeader Component</h2>
        <AdminProductHeader 
          mode="view"
          :product="testProduct"
          title="Test Product"
          subtitle="This is a test product"
          @delete="handleDelete"
          @duplicate="handleDuplicate"
        />
      </section>

      <!-- Test AdminProductInfoCard -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminProductInfoCard Component</h2>
        <AdminProductInfoCard 
          :product="enrichedTestProduct"
          :loading="false"
          @edit="handleEdit"
        />
      </section>

      <!-- Test AdminCompanySelector -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminCompanySelector Component</h2>
        <AdminCompanySelector
          v-model="selectedCompany"
          label="Test Company Selector"
          placeholder="Search for a company..."
          :required="true"
          help-text="This is a test of the company selector"
          @change="handleCompanyChange"
        />
        <p>Selected Company ID: {{ selectedCompany }}</p>
      </section>

      <!-- Test AdminCategorySelector -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminCategorySelector Component</h2>
        <AdminCategorySelector
          v-model="selectedCategory"
          label="Test Category Selector"
          placeholder="Search for a category..."
          :required="true"
          help-text="This is a test of the category selector"
          @change="handleCategoryChange"
        />
        <p>Selected Category ID: {{ selectedCategory }}</p>
      </section>

      <!-- Test AdminProductAttributesEditor -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminProductAttributesEditor Component</h2>
        <AdminProductAttributesEditor
          v-model="testAttributes"
          :show-json-preview="true"
          @change="handleAttributesChange"
        />
      </section>

      <!-- Test AdminImageUploader -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">AdminImageUploader Component</h2>
        <AdminImageUploader
          v-model="testImages"
          title="Test Image Uploader"
          help-text="Upload test images here"
          :multiple="true"
          @upload="handleImageUpload"
          @remove="handleImageRemove"
          @error="handleImageError"
        />
      </section>

      <!-- Test ProductStatusUpdateModal -->
      <section class="admin-test-section">
        <h2 class="admin-test-section-title">ProductStatusUpdateModal Component</h2>
        <button 
          class="admin-btn admin-btn-primary"
          @click="showStatusModal = true"
        >
          Open Status Update Modal
        </button>
        
        <ProductStatusUpdateModal
          v-model="showStatusModal"
          :product="testProduct"
          @updated="handleStatusUpdated"
          @close="showStatusModal = false"
        />
      </section>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import AdminProductHeader from './AdminProductHeader.vue';
import AdminProductInfoCard from './AdminProductInfoCard.vue';
import AdminCompanySelector from './AdminCompanySelector.vue';
import AdminCategorySelector from './AdminCategorySelector.vue';
import AdminProductAttributesEditor from './AdminProductAttributesEditor.vue';
import AdminImageUploader from './AdminImageUploader.vue';
import ProductStatusUpdateModal from './ProductStatusUpdateModal.vue';

// Test data
const testProduct = ref({
  id: 1,
  name: 'Test Product',
  slug: 'test-product',
  description: 'This is a test product for component testing',
  companyId: 1,
  companyName: 'Test Company',
  categoryId: 1,
  categoryName: 'Test Category',
  priceAmount: 99.99,
  priceCurrency: 'UAH',
  stock: 50,
  status: 1,
  metaTitle: 'Test Product - Meta Title',
  metaDescription: 'This is a test meta description for the test product',
  metaImage: 'https://via.placeholder.com/300x200.png?text=Test+Product',
  createdAt: '2025-06-27T10:00:00Z',
  updatedAt: '2025-06-27T12:00:00Z'
});

const enrichedTestProduct = ref({
  ...testProduct.value,
  // Add any additional computed properties here
});

// Reactive data for testing
const selectedCompany = ref(null);
const selectedCategory = ref(null);
const testAttributes = ref({
  color: 'Red',
  size: 'Large',
  material: 'Cotton'
});
const testImages = ref([]);
const showStatusModal = ref(false);

// Event handlers
const handleDelete = () => {
  console.log('Delete clicked');
  alert('Delete functionality would be called here');
};

const handleDuplicate = () => {
  console.log('Duplicate clicked');
  alert('Duplicate functionality would be called here');
};

const handleEdit = () => {
  console.log('Edit clicked');
  alert('Edit functionality would be called here');
};

const handleCompanyChange = (companyId) => {
  console.log('Company changed:', companyId);
};

const handleCategoryChange = (categoryId) => {
  console.log('Category changed:', categoryId);
};

const handleAttributesChange = (attributes) => {
  console.log('Attributes changed:', attributes);
};

const handleImageUpload = (files) => {
  console.log('Images uploaded:', files);
};

const handleImageRemove = (index) => {
  console.log('Image removed:', index);
};

const handleImageError = (error) => {
  console.log('Image error:', error);
};

const handleStatusUpdated = (data) => {
  console.log('Status updated:', data);
  alert(`Status updated to: ${data.newStatus}`);
};
</script>

<style scoped>
.admin-test-page {
  padding: var(--admin-space-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.admin-test-header {
  text-align: center;
  margin-bottom: var(--admin-space-xl);
}

.admin-test-title {
  font-size: var(--admin-text-3xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-md) 0;
}

.admin-test-description {
  font-size: var(--admin-text-lg);
  color: var(--admin-text-muted);
  margin: 0;
}

.admin-test-sections {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xl);
}

.admin-test-section {
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  background: var(--admin-bg-primary);
}

.admin-test-section-title {
  font-size: var(--admin-text-xl);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-lg) 0;
  padding-bottom: var(--admin-space-md);
  border-bottom: 1px solid var(--admin-border-light);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-test-page {
    padding: var(--admin-space-md);
  }
  
  .admin-test-section {
    padding: var(--admin-space-md);
  }
}
</style>
