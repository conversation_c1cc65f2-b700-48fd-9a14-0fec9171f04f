<template>
  <div class="admin-attributes-editor">
    <div class="admin-attributes-header">
      <h4 class="admin-attributes-title">
        <i class="fas fa-tags"></i>
        Product Attributes
      </h4>
      <button
        type="button"
        class="admin-btn admin-btn-sm admin-btn-primary"
        @click="addAttribute"
      >
        <i class="fas fa-plus"></i>
        Add Attribute
      </button>
    </div>

    <!-- Attributes List -->
    <div v-if="localAttributes.length > 0" class="admin-attributes-list">
      <div
        v-for="(attribute, index) in localAttributes"
        :key="index"
        class="admin-attribute-item"
      >
        <div class="admin-attribute-fields">
          <!-- Key Field -->
          <div class="admin-field-group">
            <label class="admin-field-label">Attribute Name</label>
            <input
              v-model="attribute.key"
              type="text"
              class="admin-field-input"
              placeholder="e.g., Color, Size, Material"
              @input="updateAttributes"
            />
          </div>

          <!-- Value Field -->
          <div class="admin-field-group">
            <label class="admin-field-label">Value</label>
            <div class="admin-value-input-wrapper">
              <input
                v-model="attribute.value"
                type="text"
                class="admin-field-input"
                :placeholder="getValuePlaceholder(attribute.type)"
                @input="updateAttributes"
              />
              <select
                v-model="attribute.type"
                class="admin-type-select"
                @change="handleTypeChange(attribute, index)"
              >
                <option value="string">Text</option>
                <option value="number">Number</option>
                <option value="boolean">Yes/No</option>
                <option value="url">URL</option>
                <option value="email">Email</option>
                <option value="date">Date</option>
                <option value="array">List</option>
              </select>
            </div>
          </div>

          <!-- Actions -->
          <div class="admin-attribute-actions">
            <button
              type="button"
              class="admin-btn admin-btn-xs admin-btn-secondary"
              @click="moveAttributeUp(index)"
              :disabled="index === 0"
              title="Move up"
            >
              <i class="fas fa-arrow-up"></i>
            </button>
            <button
              type="button"
              class="admin-btn admin-btn-xs admin-btn-secondary"
              @click="moveAttributeDown(index)"
              :disabled="index === localAttributes.length - 1"
              title="Move down"
            >
              <i class="fas fa-arrow-down"></i>
            </button>
            <button
              type="button"
              class="admin-btn admin-btn-xs admin-btn-danger"
              @click="removeAttribute(index)"
              title="Remove attribute"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Type-specific inputs -->
        <div v-if="attribute.type === 'boolean'" class="admin-boolean-input">
          <label class="admin-checkbox-label">
            <input
              type="checkbox"
              v-model="attribute.booleanValue"
              @change="handleBooleanChange(attribute, index)"
            />
            <span class="admin-checkbox-text">{{ attribute.booleanValue ? 'Yes' : 'No' }}</span>
          </label>
        </div>

        <div v-if="attribute.type === 'array'" class="admin-array-input">
          <label class="admin-field-label">List Items (one per line)</label>
          <textarea
            v-model="attribute.arrayValue"
            class="admin-field-textarea"
            placeholder="Enter each item on a new line"
            rows="3"
            @input="handleArrayChange(attribute, index)"
          ></textarea>
        </div>

        <!-- Preview -->
        <div class="admin-attribute-preview">
          <span class="admin-preview-label">Preview:</span>
          <span class="admin-preview-value" :class="getPreviewClass(attribute.type)">
            {{ formatPreviewValue(attribute) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="admin-empty-state">
      <div class="admin-empty-icon">
        <i class="fas fa-tags"></i>
      </div>
      <h4 class="admin-empty-title">No Attributes</h4>
      <p class="admin-empty-description">
        Add custom attributes to provide additional product information like color, size, material, etc.
      </p>
      <button
        type="button"
        class="admin-btn admin-btn-primary"
        @click="addAttribute"
      >
        <i class="fas fa-plus"></i>
        Add First Attribute
      </button>
    </div>

    <!-- JSON Preview (for debugging) -->
    <div v-if="showJsonPreview && localAttributes.length > 0" class="admin-json-preview">
      <details>
        <summary class="admin-json-summary">
          <i class="fas fa-code"></i>
          JSON Preview
        </summary>
        <pre class="admin-json-content">{{ JSON.stringify(attributesObject, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Object, Array],
    default: null
  },
  showJsonPreview: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change']);

// Reactive data
const localAttributes = ref([]);

// Computed
const attributesObject = computed(() => {
  const obj = {};
  localAttributes.value.forEach(attr => {
    if (attr.key && attr.key.trim()) {
      obj[attr.key.trim()] = attr.finalValue !== undefined ? attr.finalValue : attr.value;
    }
  });
  return obj;
});

// Methods
const addAttribute = () => {
  localAttributes.value.push({
    key: '',
    value: '',
    type: 'string',
    booleanValue: false,
    arrayValue: '',
    finalValue: ''
  });
};

const removeAttribute = (index) => {
  localAttributes.value.splice(index, 1);
  updateAttributes();
};

const moveAttributeUp = (index) => {
  if (index > 0) {
    const item = localAttributes.value.splice(index, 1)[0];
    localAttributes.value.splice(index - 1, 0, item);
    updateAttributes();
  }
};

const moveAttributeDown = (index) => {
  if (index < localAttributes.value.length - 1) {
    const item = localAttributes.value.splice(index, 1)[0];
    localAttributes.value.splice(index + 1, 0, item);
    updateAttributes();
  }
};

const handleTypeChange = (attribute, index) => {
  // Reset values when type changes
  attribute.value = '';
  attribute.booleanValue = false;
  attribute.arrayValue = '';
  attribute.finalValue = '';
  updateAttributes();
};

const handleBooleanChange = (attribute, index) => {
  attribute.finalValue = attribute.booleanValue;
  updateAttributes();
};

const handleArrayChange = (attribute, index) => {
  const items = attribute.arrayValue.split('\n').filter(item => item.trim());
  attribute.finalValue = items;
  updateAttributes();
};

const updateAttributes = () => {
  // Process each attribute based on its type
  localAttributes.value.forEach(attr => {
    if (attr.type === 'boolean') {
      attr.finalValue = attr.booleanValue;
    } else if (attr.type === 'array') {
      const items = attr.arrayValue.split('\n').filter(item => item.trim());
      attr.finalValue = items;
    } else if (attr.type === 'number') {
      const num = parseFloat(attr.value);
      attr.finalValue = isNaN(num) ? attr.value : num;
    } else {
      attr.finalValue = attr.value;
    }
  });

  emit('update:modelValue', attributesObject.value);
  emit('change', attributesObject.value);
};

const getValuePlaceholder = (type) => {
  switch (type) {
    case 'string': return 'Enter text value';
    case 'number': return 'Enter number';
    case 'url': return 'https://example.com';
    case 'email': return '<EMAIL>';
    case 'date': return 'YYYY-MM-DD';
    case 'boolean': return 'Use checkbox below';
    case 'array': return 'Use text area below';
    default: return 'Enter value';
  }
};

const formatPreviewValue = (attribute) => {
  if (!attribute.key) return 'Enter attribute name';
  
  let value = attribute.finalValue !== undefined ? attribute.finalValue : attribute.value;
  
  if (attribute.type === 'boolean') {
    value = attribute.booleanValue ? 'Yes' : 'No';
  } else if (attribute.type === 'array') {
    const items = attribute.arrayValue.split('\n').filter(item => item.trim());
    value = items.length > 0 ? items.join(', ') : 'No items';
  }
  
  return value || 'No value';
};

const getPreviewClass = (type) => {
  return {
    'admin-preview-value--boolean': type === 'boolean',
    'admin-preview-value--number': type === 'number',
    'admin-preview-value--url': type === 'url',
    'admin-preview-value--email': type === 'email',
    'admin-preview-value--date': type === 'date',
    'admin-preview-value--array': type === 'array'
  };
};

const parseInitialValue = (value) => {
  if (!value) return [];
  
  try {
    let parsed = value;
    
    // If it's a string, try to parse as JSON
    if (typeof value === 'string') {
      parsed = JSON.parse(value);
    }
    
    // Convert object to array format
    if (typeof parsed === 'object' && !Array.isArray(parsed)) {
      return Object.entries(parsed).map(([key, val]) => ({
        key,
        value: typeof val === 'string' ? val : JSON.stringify(val),
        type: detectType(val),
        booleanValue: typeof val === 'boolean' ? val : false,
        arrayValue: Array.isArray(val) ? val.join('\n') : '',
        finalValue: val
      }));
    }
    
    // If it's already an array
    if (Array.isArray(parsed)) {
      return parsed.map(item => ({
        key: item.key || '',
        value: item.value || '',
        type: item.type || 'string',
        booleanValue: item.booleanValue || false,
        arrayValue: item.arrayValue || '',
        finalValue: item.finalValue
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Error parsing attributes:', error);
    return [];
  }
};

const detectType = (value) => {
  if (typeof value === 'boolean') return 'boolean';
  if (typeof value === 'number') return 'number';
  if (Array.isArray(value)) return 'array';
  if (typeof value === 'string') {
    if (value.startsWith('http://') || value.startsWith('https://')) return 'url';
    if (value.includes('@') && value.includes('.')) return 'email';
    if (!isNaN(Date.parse(value)) && value.includes('-')) return 'date';
  }
  return 'string';
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  localAttributes.value = parseInitialValue(newValue);
}, { immediate: true });

// Lifecycle
onMounted(() => {
  if (props.modelValue) {
    localAttributes.value = parseInitialValue(props.modelValue);
  }
});
</script>

<style scoped>
.admin-attributes-editor {
  width: 100%;
}

.admin-attributes-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--admin-space-lg);
}

.admin-attributes-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-attributes-title i {
  color: var(--admin-primary);
}

.admin-attributes-list {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-lg);
}

.admin-attribute-item {
  padding: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  background: var(--admin-bg-secondary);
}

.admin-attribute-fields {
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: var(--admin-space-md);
  align-items: end;
}

.admin-field-group {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-field-label {
  font-size: var(--admin-text-sm);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-field-input,
.admin-field-textarea {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-base);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  transition: border-color var(--admin-transition-base);
}

.admin-field-input:focus,
.admin-field-textarea:focus {
  outline: none;
  border-color: var(--admin-primary);
}

.admin-value-input-wrapper {
  display: flex;
  gap: var(--admin-space-xs);
}

.admin-value-input-wrapper .admin-field-input {
  flex: 1;
}

.admin-type-select {
  padding: var(--admin-space-sm);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  background: var(--admin-bg-primary);
  color: var(--admin-text-primary);
  font-size: var(--admin-text-sm);
  min-width: 100px;
}

.admin-attribute-actions {
  display: flex;
  flex-direction: column;
  gap: var(--admin-space-xs);
}

.admin-boolean-input,
.admin-array-input {
  margin-top: var(--admin-space-md);
}

.admin-checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  cursor: pointer;
}

.admin-checkbox-text {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-attribute-preview {
  margin-top: var(--admin-space-md);
  padding: var(--admin-space-sm);
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-sm);
}

.admin-preview-label {
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-muted);
}

.admin-preview-value {
  color: var(--admin-text-primary);
  font-family: var(--admin-font-mono);
}

.admin-preview-value--boolean {
  color: var(--admin-info);
}

.admin-preview-value--number {
  color: var(--admin-success);
}

.admin-preview-value--url,
.admin-preview-value--email {
  color: var(--admin-link);
}

.admin-preview-value--date {
  color: var(--admin-warning);
}

.admin-preview-value--array {
  color: var(--admin-primary);
}

/* Empty State */
.admin-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--admin-space-xl);
  text-align: center;
  border: 2px dashed var(--admin-border-light);
  border-radius: var(--admin-radius-md);
}

.admin-empty-icon {
  font-size: 3rem;
  color: var(--admin-text-muted);
  margin-bottom: var(--admin-space-md);
}

.admin-empty-title {
  font-size: var(--admin-text-lg);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-sm) 0;
}

.admin-empty-description {
  font-size: var(--admin-text-sm);
  color: var(--admin-text-muted);
  margin: 0 0 var(--admin-space-lg) 0;
  max-width: 400px;
  line-height: 1.5;
}

/* JSON Preview */
.admin-json-preview {
  margin-top: var(--admin-space-lg);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-md);
  overflow: hidden;
}

.admin-json-summary {
  padding: var(--admin-space-md);
  background: var(--admin-bg-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-weight: var(--admin-font-semibold);
  color: var(--admin-text-primary);
}

.admin-json-content {
  padding: var(--admin-space-md);
  background: var(--admin-bg-primary);
  font-family: var(--admin-font-mono);
  font-size: var(--admin-text-sm);
  color: var(--admin-text-primary);
  margin: 0;
  overflow-x: auto;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-attribute-fields {
    grid-template-columns: 1fr;
    gap: var(--admin-space-sm);
  }

  .admin-attribute-actions {
    flex-direction: row;
    justify-content: center;
  }

  .admin-attributes-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-md);
  }

  .admin-value-input-wrapper {
    flex-direction: column;
  }
}
</style>
