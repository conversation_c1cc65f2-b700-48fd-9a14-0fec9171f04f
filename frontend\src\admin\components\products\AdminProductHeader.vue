<template>
  <div class="admin-page-header">
    <div class="admin-page-header-content">
      <div class="admin-page-header-info">
        <div class="admin-page-breadcrumb">
          <router-link to="/admin/products" class="admin-breadcrumb-link">
            <i class="fas fa-box"></i>
            Products
          </router-link>
          <i class="fas fa-chevron-right admin-breadcrumb-separator"></i>
          <span class="admin-breadcrumb-current">{{ breadcrumbText }}</span>
        </div>
        
        <h1 class="admin-page-title">
          <i :class="titleIcon" class="admin-page-title-icon"></i>
          {{ title }}
        </h1>
        
        <div v-if="subtitle" class="admin-page-subtitle">
          {{ subtitle }}
        </div>
        
        <div v-if="product" class="admin-page-meta">
          <div class="admin-meta-item">
            <span class="admin-meta-label">ID:</span>
            <span class="admin-meta-value">{{ product.id }}</span>
          </div>
          <div v-if="product.createdAt" class="admin-meta-item">
            <span class="admin-meta-label">Created:</span>
            <span class="admin-meta-value">{{ formatDate(product.createdAt) }}</span>
          </div>
          <div v-if="product.updatedAt" class="admin-meta-item">
            <span class="admin-meta-label">Updated:</span>
            <span class="admin-meta-value">{{ formatDate(product.updatedAt) }}</span>
          </div>
        </div>
      </div>
      
      <div class="admin-page-actions">
        <slot name="actions">
          <!-- Default actions based on mode -->
          <template v-if="mode === 'view'">
            <router-link 
              :to="`/admin/products/${product?.id}/edit`"
              class="admin-btn admin-btn-primary"
            >
              <i class="fas fa-edit"></i>
              Edit Product
            </router-link>
            <button 
              class="admin-btn admin-btn-secondary"
              @click="$emit('duplicate')"
              :disabled="!product"
            >
              <i class="fas fa-copy"></i>
              Duplicate
            </button>
            <button 
              class="admin-btn admin-btn-danger"
              @click="$emit('delete')"
              :disabled="!product"
            >
              <i class="fas fa-trash"></i>
              Delete
            </button>
          </template>
          
          <template v-else-if="mode === 'edit'">
            <button 
              class="admin-btn admin-btn-success"
              @click="$emit('save')"
              :disabled="!canSave"
              :class="{ 'admin-btn--loading': saving }"
            >
              <i class="fas fa-save"></i>
              {{ saving ? 'Saving...' : 'Save Changes' }}
            </button>
            <router-link 
              :to="product?.id ? `/admin/products/${product.id}` : '/admin/products'"
              class="admin-btn admin-btn-secondary"
            >
              <i class="fas fa-times"></i>
              Cancel
            </router-link>
          </template>
          
          <template v-else-if="mode === 'create'">
            <button 
              class="admin-btn admin-btn-success"
              @click="$emit('create')"
              :disabled="!canCreate"
              :class="{ 'admin-btn--loading': creating }"
            >
              <i class="fas fa-plus"></i>
              {{ creating ? 'Creating...' : 'Create Product' }}
            </button>
            <router-link 
              to="/admin/products"
              class="admin-btn admin-btn-secondary"
            >
              <i class="fas fa-times"></i>
              Cancel
            </router-link>
          </template>
        </slot>
      </div>
    </div>
    
    <!-- Status bar for product -->
    <div v-if="product && mode === 'view'" class="admin-page-status-bar">
      <div class="admin-status-items">
        <div class="admin-status-item">
          <span class="admin-status-label">Status:</span>
          <span class="admin-status-badge" :class="getStatusClass(product.status)">
            <i class="fas" :class="getStatusIcon(product.status)"></i>
            {{ formatStatus(product.status) }}
          </span>
        </div>
        
        <div v-if="product.companyName" class="admin-status-item">
          <span class="admin-status-label">Company:</span>
          <router-link 
            :to="`/admin/companies/${product.companyId}`"
            class="admin-status-link"
          >
            {{ product.companyName }}
          </router-link>
        </div>
        
        <div v-if="product.categoryName" class="admin-status-item">
          <span class="admin-status-label">Category:</span>
          <span class="admin-status-value">{{ product.categoryName }}</span>
        </div>
        
        <div class="admin-status-item">
          <span class="admin-status-label">Price:</span>
          <span class="admin-status-value admin-status-value--price">
            {{ formatPrice(product.priceAmount, product.priceCurrency) }}
          </span>
        </div>
        
        <div class="admin-status-item">
          <span class="admin-status-label">Stock:</span>
          <span class="admin-status-value" :class="getStockClass(product.stock)">
            {{ product.stock || 0 }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  mode: {
    type: String,
    required: true,
    validator: (value) => ['view', 'edit', 'create'].includes(value)
  },
  product: {
    type: Object,
    default: null
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  canSave: {
    type: Boolean,
    default: true
  },
  canCreate: {
    type: Boolean,
    default: true
  },
  saving: {
    type: Boolean,
    default: false
  },
  creating: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['save', 'create', 'delete', 'duplicate']);

// Computed
const titleIcon = computed(() => {
  switch (props.mode) {
    case 'view': return 'fas fa-eye';
    case 'edit': return 'fas fa-edit';
    case 'create': return 'fas fa-plus';
    default: return 'fas fa-box';
  }
});

const breadcrumbText = computed(() => {
  switch (props.mode) {
    case 'view': return props.product?.name || 'Product Details';
    case 'edit': return 'Edit Product';
    case 'create': return 'Create Product';
    default: return 'Product';
  }
});

// Methods
const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatPrice = (amount, currency = 'UAH') => {
  if (!amount && amount !== 0) return 'N/A';
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: currency || 'UAH'
  }).format(amount);
};

const formatStatus = (status) => {
  switch (status) {
    case 0: return 'PENDING';
    case 1: return 'APPROVED';
    case 2: return 'REJECTED';
    default: return 'UNKNOWN';
  }
};

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'admin-status-badge--warning';
    case 1: return 'admin-status-badge--success';
    case 2: return 'admin-status-badge--danger';
    default: return 'admin-status-badge--secondary';
  }
};

const getStatusIcon = (status) => {
  switch (status) {
    case 0: return 'fa-clock';
    case 1: return 'fa-check-circle';
    case 2: return 'fa-times-circle';
    default: return 'fa-question-circle';
  }
};

const getStockClass = (stock) => {
  if (stock === 0) return 'admin-status-value--danger';
  if (stock < 10) return 'admin-status-value--warning';
  return 'admin-status-value--success';
};
</script>

<style scoped>
.admin-page-header {
  background: var(--admin-bg-primary);
  border-bottom: 1px solid var(--admin-border-light);
  margin-bottom: var(--admin-space-lg);
}

.admin-page-header-content {
  padding: var(--admin-space-lg);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--admin-space-lg);
}

.admin-page-header-info {
  flex: 1;
  min-width: 0;
}

.admin-page-breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  margin-bottom: var(--admin-space-sm);
  font-size: var(--admin-text-sm);
}

.admin-breadcrumb-link {
  color: var(--admin-link);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  transition: color var(--admin-transition-base);
}

.admin-breadcrumb-link:hover {
  color: var(--admin-link-hover);
}

.admin-breadcrumb-separator {
  color: var(--admin-text-muted);
  font-size: 0.75rem;
}

.admin-breadcrumb-current {
  color: var(--admin-text-muted);
  font-weight: var(--admin-font-medium);
}

.admin-page-title {
  font-size: var(--admin-text-2xl);
  font-weight: var(--admin-font-bold);
  color: var(--admin-text-primary);
  margin: 0 0 var(--admin-space-xs) 0;
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
}

.admin-page-title-icon {
  color: var(--admin-primary);
}

.admin-page-subtitle {
  font-size: var(--admin-text-base);
  color: var(--admin-text-muted);
  margin-bottom: var(--admin-space-sm);
}

.admin-page-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-md);
  font-size: var(--admin-text-sm);
}

.admin-meta-item {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
}

.admin-meta-label {
  color: var(--admin-text-muted);
  font-weight: var(--admin-font-medium);
}

.admin-meta-value {
  color: var(--admin-text-primary);
  font-family: var(--admin-font-mono);
}

.admin-page-actions {
  display: flex;
  align-items: center;
  gap: var(--admin-space-sm);
  flex-shrink: 0;
}

/* Status bar */
.admin-page-status-bar {
  padding: var(--admin-space-md) var(--admin-space-lg);
  background: var(--admin-bg-secondary);
  border-top: 1px solid var(--admin-border-light);
}

.admin-status-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--admin-space-lg);
  align-items: center;
}

.admin-status-item {
  display: flex;
  align-items: center;
  gap: var(--admin-space-xs);
  font-size: var(--admin-text-sm);
}

.admin-status-label {
  color: var(--admin-text-muted);
  font-weight: var(--admin-font-medium);
}

.admin-status-value {
  color: var(--admin-text-primary);
  font-weight: var(--admin-font-medium);
}

.admin-status-value--price {
  color: var(--admin-success);
  font-weight: var(--admin-font-semibold);
}

.admin-status-value--success {
  color: var(--admin-success);
}

.admin-status-value--warning {
  color: var(--admin-warning);
}

.admin-status-value--danger {
  color: var(--admin-danger);
}

.admin-status-link {
  color: var(--admin-link);
  text-decoration: none;
  font-weight: var(--admin-font-medium);
  transition: color var(--admin-transition-base);
}

.admin-status-link:hover {
  color: var(--admin-link-hover);
}

.admin-status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--admin-space-xs);
  padding: var(--admin-space-xs) var(--admin-space-sm);
  border-radius: var(--admin-radius-sm);
  font-size: var(--admin-text-xs);
  font-weight: var(--admin-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-status-badge--success {
  background: var(--admin-success);
  color: var(--admin-text-white);
}

.admin-status-badge--warning {
  background: var(--admin-warning);
  color: var(--admin-text-primary);
}

.admin-status-badge--danger {
  background: var(--admin-danger);
  color: var(--admin-text-white);
}

.admin-status-badge--secondary {
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-muted);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-page-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-md);
  }
  
  .admin-page-actions {
    justify-content: stretch;
  }
  
  .admin-page-actions .admin-btn {
    flex: 1;
  }
  
  .admin-page-meta {
    flex-direction: column;
    gap: var(--admin-space-sm);
  }
  
  .admin-status-items {
    flex-direction: column;
    align-items: stretch;
    gap: var(--admin-space-sm);
  }
  
  .admin-status-item {
    justify-content: space-between;
  }
}
</style>
