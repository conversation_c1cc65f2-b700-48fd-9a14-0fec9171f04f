<template>
  <div class="product-list">
    <div class="level">
      <div class="level-left">
        <div class="level-item">
          <h1 class="title">Products</h1>
        </div>
      </div>
      <div class="level-right">
        <div class="level-item">
          <router-link to="/admin/products/create" class="button is-primary">
            <span class="icon">
              <i class="fas fa-plus"></i>
            </span>
            <span>Add Product</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
      <div class="card-content">
        <div class="columns is-multiline">
          <div class="column is-3">
            <div class="field">
              <label class="label">Search</label>
              <div class="control has-icons-left">
                <input
                  class="input"
                  type="text"
                  placeholder="Search by name, SKU, description..."
                  v-model="filters.search"
                  @input="debouncedSearch">
                <span class="icon is-small is-left">
                  <i class="fas fa-search"></i>
                </span>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Category</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.categoryId" @change="fetchProducts(1)">
                    <option value="">All Categories</option>
                    <option
                      v-for="category in categories"
                      :key="category.id"
                      :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Status</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.status" @change="fetchProducts(1)">
                    <option value="">All Statuses</option>
                    <option value="Pending">Pending</option>
                    <option value="Approved">Approved</option>
                    <option value="Rejected">Rejected</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-3">
            <div class="field">
              <label class="label">Stock</label>
              <div class="control">
                <div class="select is-fullwidth">
                  <select v-model="filters.stock" @change="fetchProducts(1)">
                    <option value="">All</option>
                    <option value="in_stock">In Stock</option>
                    <option value="low_stock">Low Stock</option>
                    <option value="out_of_stock">Out of Stock</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="column is-12">
            <div class="field is-grouped is-grouped-right">
              <div class="control">
                <button 
                  class="button is-light" 
                  @click="resetFilters">
                  Reset
                </button>
              </div>
              <div class="control">
                <button 
                  class="button is-primary" 
                  @click="fetchProducts"
                  :class="{ 'is-loading': loading }">
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Products Table -->
    <div class="card">
      <div class="card-content">
        <div v-if="loading && !products.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-spinner fa-pulse fa-2x"></i>
          </span>
          <p class="mt-2">Loading products...</p>
        </div>
        <div v-else-if="!products.length" class="has-text-centered py-6">
          <span class="icon is-large">
            <i class="fas fa-box fa-2x"></i>
          </span>
          <p class="mt-2">No products found</p>
          <p class="mt-2">Try adjusting your filters or add a new product</p>
          <div class="mt-4">
            <router-link to="/admin/products/create" class="button is-primary">
              <span class="icon">
                <i class="fas fa-plus"></i>
              </span>
              <span>Add Product</span>
            </router-link>
          </div>
        </div>
        <div v-else>
          <div class="table-container">
            <table class="table is-fullwidth is-hoverable">
              <thead>
                <tr>
                  <th>Image</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="product in products" :key="product.id">
                  <td class="image-cell">
                    <figure class="image is-48x48">
                      <img
                        :src="getImageSrc(product)"
                        :alt="product.name"
                        @error="handleImageError($event, product)">
                    </figure>
                  </td>
                  <td>
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-sku">SKU: {{ product.sku || 'N/A' }}</div>
                  </td>
                  <td>{{ getCategoryName(product.categoryId) }}</td>
                  <td>{{ formatCurrency(product.priceAmount || product.price) }}</td>
                  <td>
                    <span 
                      class="tag" 
                      :class="getStockClass(product.stock)">
                      {{ product.stock }}
                    </span>
                  </td>
                  <td>
                    <status-badge 
                      :status="product.status" 
                      type="product" />
                  </td>
                  <td>
                    <div class="buttons are-small">
                      <router-link 
                        :to="`/admin/products/${product.id}`" 
                        class="button is-info" 
                        title="View">
                        <span class="icon is-small">
                          <i class="fas fa-eye"></i>
                        </span>
                      </router-link>
                      <router-link
                        :to="`/admin/products/${product.id}/edit`"
                        class="button is-primary"
                        title="Edit">
                        <span class="icon is-small">
                          <i class="fas fa-edit"></i>
                        </span>
                      </router-link>
                      <button
                        class="button is-warning"
                        @click="openStatusUpdateModal(product)"
                        title="Update Status">
                        <span class="icon is-small">
                          <i class="fas fa-tasks"></i>
                        </span>
                      </button>
                      <button
                        class="button is-danger"
                        @click="confirmDelete(product)"
                        title="Delete">
                        <span class="icon is-small">
                          <i class="fas fa-trash"></i>
                        </span>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <pagination 
            :current-page="currentPage" 
            :total-pages="totalPages"
            @page-changed="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <confirm-dialog
      :is-open="showDeleteModal"
      title="Delete Product"
      :message="`Are you sure you want to delete '${productToDelete?.name}'? This action cannot be undone.`"
      confirm-text="Delete"
      cancel-text="Cancel"
      @confirm="deleteProduct"
      @cancel="cancelDelete" />

    <!-- Status Update Modal -->
    <ProductStatusUpdateModal
      v-model="showStatusUpdateModal"
      :product="productToUpdate"
      @updated="handleStatusUpdated"
      @close="closeStatusUpdateModal"
    />

    <!-- Debug Info -->
    <div v-if="showStatusUpdateModal" style="position: fixed; top: 10px; right: 10px; background: red; color: white; padding: 10px; z-index: 9999;">
      Modal should be visible! Product: {{ productToUpdate?.name }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { productsService } from '@/admin/services/products';
import { categoriesService } from '@/admin/services/categories';
import StatusBadge from '@/admin/components/common/StatusBadge.vue';
import Pagination from '@/admin/components/common/Pagination.vue';
import ConfirmDialog from '@/admin/components/common/ConfirmDialog.vue';
import ProductStatusUpdateModal from '@/admin/components/products/ProductStatusUpdateModal.vue';

// State
const products = ref([]);
const categories = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(10);
const showDeleteModal = ref(false);
const productToDelete = ref(null);
const showStatusUpdateModal = ref(false);
const productToUpdate = ref(null);
const searchTimeout = ref(null);

// Filters
const filters = reactive({
  search: '',
  categoryId: '',
  status: '',
  stock: ''
});

// Fetch products
const fetchProducts = async (page = 1) => {
  loading.value = true;
  currentPage.value = page;
  
  try {
    const response = await productsService.getProducts({
      page: currentPage.value,
      pageSize: itemsPerPage.value,
      limit: itemsPerPage.value,
      ...filters
    });

    console.log('Products API response:', response);

    if (response.data) {
      products.value = response.data;
      console.log('Products loaded:', products.value.length);
    } else if (response.products) {
      products.value = response.products;
      console.log('Products loaded (products field):', products.value.length);
    } else if (response.items) {
      products.value = response.items;
      console.log('Products loaded (items field):', products.value.length);
    } else {
      products.value = [];
      console.log('No products found in response');
    }

    if (response.pagination) {
      totalPages.value = response.pagination.totalPages;
      totalItems.value = response.pagination.total;
      currentPage.value = response.pagination.page;
      console.log('Pagination:', response.pagination);
    }
  } catch (error) {
    console.error('Error fetching products:', error);
  } finally {
    loading.value = false;
  }
};

// Fetch categories
const fetchCategories = async () => {
  try {
    const response = await categoriesService.getCategories();
    console.log('Categories API response:', response);

    // Handle different response structures
    if (response.categories) {
      categories.value = response.categories;
    } else if (response.data) {
      categories.value = response.data;
    } else if (Array.isArray(response)) {
      categories.value = response;
    } else {
      categories.value = [];
    }

    console.log('Categories loaded:', categories.value.length);
  } catch (error) {
    console.error('Error fetching categories:', error);
    categories.value = [];
  }
};

// Reset filters
const resetFilters = () => {
  filters.search = '';
  filters.categoryId = '';
  filters.status = '';
  filters.stock = '';
  fetchProducts(1);
};

// Handle page change
const handlePageChange = (page) => {
  fetchProducts(page);
};

// Debounced search
const debouncedSearch = () => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  searchTimeout.value = setTimeout(() => {
    fetchProducts(1);
    searchTimeout.value = null;
  }, 500); // 500ms debounce
};

// Get category name
const getCategoryName = (categoryId) => {
  const category = categories.value.find(c => c.id === categoryId);
  return category ? category.name : 'Unknown';
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: 'UAH'
  }).format(value);
};

// Get stock class
const getStockClass = (stock) => {
  if (stock <= 0) {
    return 'is-danger';
  } else if (stock <= 5) {
    return 'is-warning';
  } else {
    return 'is-success';
  }
};

// Get image source with fallback
const getImageSrc = (product) => {
  if (product.image && product.image.trim() !== '') {
    return product.image;
  }
  // Return a data URL for a simple gray placeholder
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhWMjAiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+YXRoIGQ9Ik0yMCAyNEwyOCAyNCIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K';
};

// Handle image error
const handleImageError = (event, product) => {
  // Prevent infinite loop by checking if already using fallback
  if (!event.target.src.startsWith('data:image/svg+xml')) {
    console.log('🖼️ Image failed to load for product:', product.name);
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiNEMUQxRDEiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8cGF0aCBkPSJNMjQgMjhWMjAiIHN0cm9rZT0iI0QxRDFEMSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPHA+YXRoIGQ9Ik0yMCAyNEwyOCAyNCIgc3Ryb2tlPSIjRDFEMUQxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K';
  }
};

// Confirm delete
const confirmDelete = (product) => {
  productToDelete.value = product;
  showDeleteModal.value = true;
};

// Delete product
const deleteProduct = async () => {
  if (!productToDelete.value) return;
  
  try {
    await productsService.deleteProduct(productToDelete.value.id);
    products.value = products.value.filter(p => p.id !== productToDelete.value.id);
    showDeleteModal.value = false;
    productToDelete.value = null;
  } catch (error) {
    console.error('Error deleting product:', error);
  }
};

// Cancel delete
const cancelDelete = () => {
  showDeleteModal.value = false;
  productToDelete.value = null;
};

// Open status update modal
const openStatusUpdateModal = (product) => {
  console.log('Opening status update modal for product:', product);
  productToUpdate.value = product;
  showStatusUpdateModal.value = true;
  console.log('Modal state:', showStatusUpdateModal.value);
};

// Handle status updated
const handleStatusUpdated = (data) => {
  console.log('Product status updated:', data);

  // Update the product in the list
  const productIndex = products.value.findIndex(p => p.id === data.product.id);
  if (productIndex !== -1) {
    products.value[productIndex].status = data.newStatus;
  }

  // Close modal
  closeStatusUpdateModal();

  // Optionally refresh the list to get updated data
  // fetchProducts();
};

// Close status update modal
const closeStatusUpdateModal = () => {
  showStatusUpdateModal.value = false;
  productToUpdate.value = null;
};

// Lifecycle hooks
onMounted(() => {
  fetchProducts();
  fetchCategories();
});
</script>

<style scoped>
.product-list {
  padding: 1rem;
}

.title {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.py-6 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.image-cell {
  width: 60px;
}

.product-name {
  font-weight: 500;
}

.product-sku {
  font-size: 0.8rem;
  color: #7a7a7a;
}

.table th {
  font-weight: 600;
  color: #363636;
  background-color: #f9f9f9;
}

.button.is-primary {
  background-color: #ff7700;
}

.button.is-primary:hover {
  background-color: #e66a00;
}

.button.is-primary.is-outlined {
  background-color: transparent;
  border-color: #ff7700;
  color: #ff7700;
}

.button.is-primary.is-outlined:hover {
  background-color: #ff7700;
  color: white;
}
</style>
