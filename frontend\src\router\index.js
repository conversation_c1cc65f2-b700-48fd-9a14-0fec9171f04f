import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';
import HomePage from '../views/home/<USER>';
import LoginPage from '../views/auth/LoginPage.vue';
import RegisterPage from '../views/auth/RegisterPage.vue';
import UserProfile from '../views/profile/UserProfile.vue';
import UserDashboardPage from '../views/UserDashboardPage.vue';
import CatalogPage from '../views/catalog/CatalogPage.vue';
import FiltersSidebar from '../components/catalog/FiltersSidebar.vue';
import CartPage from '../views/cart/CartPage.vue';

// Lazy-loaded components
const Cart = () => import('../views/cart/CartPage.vue');
const NotFound = () => import('../views/NotFoundPage.vue');

// Admin components
const AdminLayout = () => import('../admin/layouts/AdminLayout.vue');
const AdminDashboard = () => import('../admin/views/Dashboard.vue');

// Admin Users
const AdminUsers = () => import('../admin/views/users/UserList.vue');
const AdminUserDetail = () => import('../admin/views/users/UserDetail.vue');
const AdminUserEdit = () => import('../admin/views/users/UserEdit.vue');
const AdminUserCreate = () => import('../admin/views/users/UserCreate.vue');

// Admin Products
const AdminProducts = () => import('../admin/views/Products.vue');
const AdminProductView = () => import('../admin/components/products/ProductView.vue');
const AdminProductEdit = () => import('../admin/components/products/ProductEdit.vue');
const AdminProductCreate = () => import('../admin/components/products/ProductCreate.vue');
const AdminProductTest = () => import('../admin/components/products/ProductTestPage.vue');
const AdminApiTest = () => import('../admin/components/products/ApiTestPage.vue');

// Admin Categories
const AdminCategories = () => import('../admin/views/Categories.vue');
const AdminCategoryView = () => import('../admin/views/CategoryView.vue');
const AdminCategoryCreate = () => import('../admin/views/CategoryCreate.vue');
const AdminCategoryEdit = () => import('../admin/views/CategoryEdit.vue');
const AdminCategoryTestPage = () => import('../admin/views/CategoryTestPage.vue');

// Admin Orders
const AdminOrders = () => import('../admin/views/orders/OrderList.vue');
const AdminOrderDetail = () => import('../admin/views/orders/OrderDetail.vue');
const AdminOrderView = () => import('../admin/views/orders/OrderView.vue');
const AdminOrderEdit = () => import('../admin/views/orders/OrderEdit.vue');

// Admin Seller Requests
const AdminSellerRequests = () => import('../admin/views/seller-requests/SellerRequestList.vue');
const AdminSellerRequestDetail = () => import('../admin/views/seller-requests/SellerRequestDetail.vue');

// Admin Companies
const AdminCompanies = () => import('../admin/views/companies/CompanyList.vue');
const AdminCompanyDetail = () => import('../admin/views/companies/CompanyDetail.vue');
const AdminCompanyEdit = () => import('../admin/views/companies/CompanyEdit.vue');

// Admin Reviews
const AdminReviews = () => import('../admin/views/reviews/ReviewList.vue');
const AdminReviewDetail = () => import('../admin/views/reviews/ReviewDetail.vue');

// Admin Ratings
const AdminRatings = () => import('../admin/views/ratings/RatingList.vue');

// Admin Chats
const AdminChats = () => import('../admin/views/chats/ChatList.vue');
const AdminChatDetail = () => import('../admin/views/chats/ChatDetail.vue');

// Admin Addresses
const AdminAddresses = () => import('../admin/views/addresses/AddressList.vue');

// Test page
const ApiTest = () => import('../admin/views/test/ApiTest.vue');

const AdminSettings = () => import('../admin/views/Settings.vue');
const AdminSecurity = () => import('../admin/views/Security.vue');
const AdminReports = () => import('../views/admin/Reports.vue');

const routes = [
  { path: '/', name: 'Home', component: HomePage },
  { path: '/login', name: 'Login', component: LoginPage, meta: { guestOnly: true } },
  { path: '/register', name: 'Register', component: RegisterPage, meta: { guestOnly: true } },
  { path: '/dashboard', name: 'Dashboard', component: UserDashboardPage, meta: { requiresAuth: true } },
  { path: '/cart', name: 'Cart', component: CartPage, meta: {requiresAuth: true} },
  { path: '/catalog/:value([a-zA-Z-0-9]+)', name: 'Catalog', component: CatalogPage, meta: {requiresAuth: false}},
  { path: '/profile', name: 'Profile', component: UserProfile, meta: { requiresAuth: true }},
  { path: '/user/profile', name: 'UserProfile', component: UserProfile, meta: { requiresAuth: true }},
  // Admin routes
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true, requiresAdminOrModerator: true },
    children: [
      {
        path: 'dashboard',
        name: 'AdminDashboard',
        component: AdminDashboard
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers
      },
      {
        path: 'users/create',
        name: 'AdminUserCreate',
        component: AdminUserCreate
      },
      {
        path: 'users/:id',
        name: 'AdminUserDetail',
        component: AdminUserDetail
      },
      {
        path: 'users/:id/edit',
        name: 'AdminUserEdit',
        component: AdminUserEdit
      },
      {
        path: 'products',
        name: 'AdminProducts',
        component: AdminProducts
      },
      {
        path: 'products/create',
        name: 'AdminProductCreate',
        component: AdminProductCreate
      },
      {
        path: 'products/:id/view',
        name: 'AdminProductView',
        component: AdminProductView
      },
      {
        path: 'products/:id/edit',
        name: 'AdminProductEdit',
        component: AdminProductEdit
      },
      {
        path: 'products/test',
        name: 'AdminProductTest',
        component: AdminProductTest
      },
      {
        path: 'api-test',
        name: 'AdminApiTest',
        component: AdminApiTest
      },
      {
        path: 'categories',
        name: 'AdminCategories',
        component: AdminCategories
      },
      {
        path: 'categories/create',
        name: 'AdminCategoryCreate',
        component: AdminCategoryCreate
      },
      {
        path: 'categories/:id',
        name: 'AdminCategoryView',
        component: AdminCategoryView
      },
      {
        path: 'categories/:id/edit',
        name: 'AdminCategoryEdit',
        component: AdminCategoryEdit
      },
      {
        path: 'categories/test',
        name: 'AdminCategoryTestPage',
        component: AdminCategoryTestPage
      },
      {
        path: 'orders',
        name: 'AdminOrders',
        component: AdminOrders
      },
      {
        path: 'orders/:id',
        name: 'AdminOrderDetail',
        component: AdminOrderDetail
      },
      {
        path: 'orders/:id/view',
        name: 'AdminOrderView',
        component: AdminOrderView
      },
      {
        path: 'orders/:id/edit',
        name: 'AdminOrderEdit',
        component: AdminOrderEdit
      },
      {
        path: 'seller-requests',
        name: 'AdminSellerRequests',
        component: AdminSellerRequests
      },
      {
        path: 'seller-requests/:id',
        name: 'AdminSellerRequestDetail',
        component: AdminSellerRequestDetail
      },
      {
        path: 'companies',
        name: 'AdminCompanies',
        component: AdminCompanies
      },
      {
        path: 'companies/:id',
        name: 'AdminCompanyDetail',
        component: AdminCompanyDetail
      },
      {
        path: 'companies/:id/edit',
        name: 'AdminCompanyEdit',
        component: AdminCompanyEdit
      },
      {
        path: 'reviews',
        name: 'AdminReviews',
        component: AdminReviews
      },
      {
        path: 'reviews/:id',
        name: 'AdminReviewDetail',
        component: AdminReviewDetail
      },
      {
        path: 'ratings',
        name: 'AdminRatings',
        component: AdminRatings
      },
      {
        path: 'chats',
        name: 'AdminChats',
        component: AdminChats
      },
      {
        path: 'chats/:id',
        name: 'AdminChatDetail',
        component: AdminChatDetail
      },
      {
        path: 'addresses',
        name: 'AdminAddresses',
        component: AdminAddresses
      },
      {
        path: 'test',
        name: 'ApiTest',
        component: ApiTest
      },
      {
        path: 'settings',
        name: 'AdminSettings',
        component: AdminSettings,
        meta: { requiresAdmin: true } // Тільки адміністратори
      },
      {
        path: 'security',
        name: 'AdminSecurity',
        component: AdminSecurity,
        meta: { requiresAdmin: true } // Тільки адміністратори
      },
      {
        path: 'reports',
        name: 'AdminReports',
        component: AdminReports
      },
      {
        path: '',
        redirect: { name: 'AdminDashboard' }
      },
      // Catch-all route for admin section
      {
        path: ':pathMatch(.*)*',
        redirect: { name: 'AdminDashboard' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Start loading state for navigation
  store.dispatch('loading/startRouteChange', 'Navigating...');

  // Cancel any pending requests from the previous route
  if (from.path) {
    const apiService = (await import('@/services/api')).default;
    apiService.cancelRequestsForRoute(from.path);
  }
  const isLoggedIn = store.getters['auth/isLoggedIn'];
  const isAdmin = store.getters['auth/isAdmin'];
  const isModerator = store.getters['auth/isModerator'];

  // Debug logging
  if (isLoggedIn) {
    const user = store.getters['auth/user'];
    console.log('Current user:', user);
    console.log('User role:', user?.role);
    console.log('Is admin?', isAdmin);
    console.log('Trying to access:', to.fullPath);

    // Direct role check for debugging
    const isAdminByDirectCheck = user?.role === 'Admin';
    console.log('Is admin by direct check?', isAdminByDirectCheck);
  }

  // Routes that require authentication
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn) {
      console.log('Not logged in, redirecting to login');
      store.dispatch('loading/finishRouteChange');
      next({ name: 'Login', query: { redirect: to.fullPath } });
    } else if (to.matched.some(record => record.meta.requiresAdminOrModerator)) {
      // Check if user is admin or moderator
      const user = store.getters['auth/user'];
      const role = user?.role;

      let isAdminOrModerator = false;

      if (typeof role === 'string') {
        isAdminOrModerator = role === 'Admin' || role === 'Moderator';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        isAdminOrModerator = role === 3 || role === 4;
      }

      if (!isAdmin && !isModerator && !isAdminOrModerator) {
        console.log('Not admin or moderator, redirecting to dashboard');
        store.dispatch('loading/finishRouteChange');
        next({ name: 'Dashboard' });
      } else {
        // Check for admin-only routes
        if (to.matched.some(record => record.meta.requiresAdmin)) {
          if (!isAdmin) {
            console.log('Admin-only route, but user is not admin, redirecting to admin dashboard');
            store.dispatch('loading/finishRouteChange');
            next({ name: 'AdminDashboard' });
          } else {
            next();
          }
        } else {
          next();
        }
      }
    } else if (to.matched.some(record => record.meta.requiresAdmin)) {
      // Check if user is admin
      const user = store.getters['auth/user'];

      // Get the role from the user object
      const role = user?.role;

      // Log detailed role information for debugging
      console.log('User role in router guard:', role);
      console.log('Role type:', typeof role);

      // Check role in a consistent way
      let isAdminRole = false;

      if (typeof role === 'string') {
        isAdminRole = role === 'Admin';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        isAdminRole = role === 4;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          isAdminRole = role.value === 'Admin' || role.value === 4;
        }
        if (role.hasOwnProperty('name')) {
          isAdminRole = role.name === 'Admin';
        }
      }

      console.log('Is admin by direct role check:', isAdminRole);
      console.log('Is admin by getter:', isAdmin);

      if (!isAdmin && !isAdminRole) {
        console.log('Not admin, redirecting to dashboard');
        store.dispatch('loading/finishRouteChange');
        next({ name: 'Dashboard' }); // Redirect non-admin users
      } else {
        console.log('Admin access granted');
        next();
      }
    } else {
      next();
    }
  }
  // Routes for guests only (login, register)
  else if (to.matched.some(record => record.meta.guestOnly)) {
    if (isLoggedIn) {
      // Check if user is admin using both methods
      const user = store.getters['auth/user'];

      // Get the role from the user object
      const role = user?.role;

      // Check role in a consistent way
      let isAdminRole = false;

      if (typeof role === 'string') {
        isAdminRole = role === 'Admin';
      } else if (typeof role === 'number') {
        // Buyer = 0, Seller = 1, SellerOwner = 2, Moderator = 3, Admin = 4
        isAdminRole = role === 4;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          isAdminRole = role.value === 'Admin' || role.value === 4;
        }
        if (role.hasOwnProperty('name')) {
          isAdminRole = role.name === 'Admin';
        }
      }

      // Також перевіряємо, чи є користувач модератором
      let isModeratorRole = false;
      if (typeof role === 'string') {
        isModeratorRole = role === 'Moderator';
      } else if (typeof role === 'number') {
        isModeratorRole = role === 3;
      } else if (role && typeof role === 'object') {
        if (role.hasOwnProperty('value')) {
          isModeratorRole = role.value === 'Moderator' || role.value === 3;
        }
        if (role.hasOwnProperty('name')) {
          isModeratorRole = role.name === 'Moderator';
        }
      }

      const shouldGoToAdmin = isAdmin || isAdminRole || isModerator || isModeratorRole;

      console.log('Guest route, user is logged in');
      console.log('Should redirect to admin?', shouldGoToAdmin);

      store.dispatch('loading/finishRouteChange');
      next({ name: shouldGoToAdmin ? 'AdminDashboard' : 'Dashboard' });
    } else {
      next();
    }
  } else {
    next();
  }
});

// After each route change
router.afterEach(() => {
  // End loading state
  store.dispatch('loading/finishRouteChange');
});
export default router;

